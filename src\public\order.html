<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>订单</title>
    <link rel="stylesheet" href="css/css-comment.css" />
    <link rel="stylesheet" href="vue/element/<EMAIL>" />
    <link rel="stylesheet" href="css/image.css" />
    <link rel="stylesheet" href="css/shop_index.css" />
    <link rel="stylesheet" href="component/css/component.css" />
    <link rel="stylesheet" href="css/print.css" />
    <link rel="stylesheet" href="css/shouyingtai_kaidan.css" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_ih4cjgf9jn.css"
    />
    <style>
      .input-group {
        display: flex;
        position: relative;
        margin-bottom: 8px;
      }
      .native-input {
        flex: 1;
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        outline: none;
        border: 1px solid #dcdfe6;
        border-radius: 4px 0 0 4px;
        padding: 0 8px;
        height: 32px;
        line-height: 32px;
      }
      .native-input:focus {
        border-color: #409eff;
      }
      .input-append {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #dcdfe6;
        border-left: 0;
        border-radius: 0 4px 4px 0;
        background-color: #f5f7fa;
        padding: 0 15px;
        height: 32px;
        color: #909399;
        font-size: 14px;
        white-space: nowrap;
      }
    </style>
  </head>

  <body>
    <div id="order" v-cloak>
      <div style="display: flex">
        <div class="left">
          <div class="left_menu">
            <ul>
              <li
                v-for="(item, index) in leftMenu"
                :class="isActive == index ? 'addclass' : '' "
                @click="getmenu(index)"
              >
                {{item.word}}
              </li>
            </ul>
            <div
              class="f-left-nav-bg transition-all"
              :style="{top:isActive*60+40+'px'}"
            ></div>
          </div>
        </div>
        <div
          class="order-container"
          v-if="isActive==0"
          style="width: calc(100% - 100px)"
        >
          <!-- <div class="shop-tab" style="width: 100%">
            <el-radio-group v-model="tabCur" size="medium" @change="bindTabCur">
              <el-radio-button label="0">全部</el-radio-button>
              <el-radio-button label="1">待付款</el-radio-button>
              <el-radio-button label="2">待发货</el-radio-button>
              <el-radio-button label="3">已发货</el-radio-button>
              <el-radio-button label="4">已完成</el-radio-button>
              <el-radio-button label="5">已取消</el-radio-button>
              <el-radio-button label="6">待收货</el-radio-button>
              <el-radio-button label="7">待核销</el-radio-button>
            </el-radio-group>
          </div> -->
          <div class="table-wrap bg-white" style="width: 100%">
            <div class="order-search">
              <div class="order-search">
                <el-input
                  placeholder="请输入订单编号或手机号"
                  suffix-icon="el-icon-search"
                  @keyup.enter.exact.native.stop="handleSearch"
                  v-model.trim="key_num"
                ></el-input>
              </div>
            </div>

            <el-table
              :data="orderTable"
              v-loading="loadingOrderTable"
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 1)"
              class="orderTable"
              ref="orderTable"
              @row-click="handleRowClick"
            >
              <el-table-column type="expand">
                <template slot-scope="props">
                  <div class="f-table-h-fit pl-14 pr-6">
                    <el-table
                      size="mini"
                      border
                      style="width: 100%"
                      :height="updateTableHeight(props.row.orderInfo.length)"
                      :data="props.row.orderInfo"
                    >
                      <el-table-column prop="name" label="商品名称">
                        <template slot-scope="subProps">
                          <span v-show="subProps.row.name">
                            {{subProps.row.name}}
                          </span>
                          <span v-show="subProps.row.sku_name">
                            | {{subProps.row.sku_name}}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="price" label="商品价格">
                        <template slot-scope="subProps">
                          <div>
                            {{formatMoney(subProps.row.price)}}
                            <span>×</span>
                            {{subProps.row.num}}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="equity_type" label="权益">
                        <template slot-scope="subProps">
                          <div>
                            <template v-if="subProps.row.equity_type == 1">
                              无权益
                            </template>
                            <template v-else-if="subProps.row.equity_type == 2">
                              <span style="color: #a5a5a5; font-size: 12px">
                                折扣优惠
                              </span>
                              <span>{{subProps.row.reduceprice}}</span>
                            </template>
                            <template v-else-if="subProps.row.equity_type == 3">
                              <span style="color: #a5a5a5; font-size: 12px">
                                会员卡抵扣1次
                              </span>
                              <span>{{subProps.row.reduceprice}}</span>
                            </template>
                            <template v-else-if="subProps.row.equity_type == 4">
                              <span style="color: #a5a5a5; font-size: 12px">
                                优惠金额
                              </span>
                              <span>{{subProps.row.reduceprice}}</span>
                            </template>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="Craftsman" label="理疗师">
                        <template slot="header" slot-scope="subProps">
                          <technician-name></technician-name>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="order_id"
                label="订单号/开单时间"
                min-width="300"
              >
                <template slot-scope="scope">
                  <div
                    v-if="scope.row.order_number"
                    class="w-fit cursor-pointer text-primary hover:text-primary/80"
                    @click.stop="bindSeeDetails(scope.row)"
                  >
                    {{scope.row.order_number}}
                  </div>
                  <div v-if="scope.row.order_time" class="text-gray text-sm">
                    {{scope.row.order_time}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" min-width="130">
                <template slot="header" slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="click"
                    v-model="typePopover"
                  >
                    <div class="popover-wrap">
                      <div>
                        <p class="popover-title">类型选择</p>
                        <span
                          class="cancel-popover"
                          @click.stop="bindIsTypeCancel"
                        >
                          取消
                        </span>
                      </div>
                      <ul class="popover-ul">
                        <li
                          class="popover-li"
                          v-for="(item,index) in labelArr"
                          @click.stop="bindLabel(item,index)"
                        >
                          <div class="o-tag" :class="item.tagClass">
                            {{item.label}}
                          </div>
                          <i
                            v-if="typeIndex==index"
                            class="el-icon-circle-check"
                          ></i>
                        </li>
                      </ul>
                    </div>
                    <span slot="reference">
                      类型
                      <i class="el-icon-caret-bottom"></i>
                    </span>
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <div class="o-tag" :class="getOrderTypeColor(scope.row.type)">
                    {{getOrderTypeName(scope.row.type)}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="客户" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.row.vip">
                    <div
                      v-if="scope.row.vip.member_name"
                      @click.stop="toCustomerPage(scope.row.vip.id)"
                      class="w-fit cursor-pointer text-primary hover:text-primary/80"
                    >
                      {{scope.row.vip.member_name}}
                    </div>
                    <div class="text-gray text-sm">{{scope.row.vip.phone}}</div>
                    <!-- <p v-if="scope.row.vip.phone" class="table_date">{{scope.row.vip.phone}}</p> -->
                  </div>
                  <span v-if="scope.row.vip_id==0"></span>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="product" label="商品">
              <template slot-scope="scope">
                  <div v-if="scope.row.orderInfo!==undefined && scope.row.orderInfo.length>0">
                      <p v-if="index<3" v-for="(item,index) in scope.row.orderInfo">
                          <span v-show="item.name">{{item.name}}</span>
                          <span v-show="item.sku_name">| {{item.sku_name}}</span>
                      </p>
                  </div>
                  <span v-else>无</span>
              </template>
          </el-table-column>
          <el-table-column prop="technician" label="">
              <template slot="header" slot-scope="scope">
                  <technician-name></technician-name>
              </template>
              <template slot-scope="scope" v-if="scope.row.orderInfo!==undefined && scope.row.orderInfo.length>0">
                  <div v-for="(item,index) in scope.row.orderInfo">
                      {{item.Craftsman}}
                  </div>
              </template>
          </el-table-column> -->
              <el-table-column
                prop="net_receipts"
                label="合计收款"
                min-width="200"
                align="center"
              >
                <template slot-scope="scope">
                  <div
                    class="pr-10 text-right font-bold"
                    :class="scope.row.state==1 || scope.row.state==5 ? 'text-gray':''"
                  >
                    {{formatMoney(scope.row.receivable)}}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="state" label="状态">
                <template slot-scope="scope">
                  <el-tag
                    v-if="scope.row.state==1"
                    size="medium"
                    type="warning"
                  >
                    待付款
                  </el-tag>
                  <span v-if="scope.row.state==2">
                    待发货
                    <span
                      v-if="scope.row.is_refund==1"
                      style="display: block; color: #3363ff; cursor: pointer"
                    >
                      已退款
                    </span>
                  </span>
                  <span v-if="scope.row.state==3">
                    已发货
                    <span
                      v-if="scope.row.is_refund==1"
                      style="display: block; color: #3363ff; cursor: pointer"
                    >
                      已退款
                    </span>
                  </span>
                  <div v-if="scope.row.state==4">
                    <el-tag type="success" size="medium">已完成</el-tag>
                    <span
                      v-if="scope.row.agency==1"
                      @click.stop="staffCollection(scope.row.id,scope.row.agencyStaffInfo.nickname,scope.row)"
                      style="display: block; color: #3363ff; cursor: pointer"
                    >
                      {{scope.row.agencyStaffInfo.nickname}}代收
                    </span>
                    <span
                      v-if="scope.row.is_verify==1"
                      style="display: block; color: #3363ff; cursor: pointer"
                    >
                      待核销
                    </span>
                    <span
                      v-if="scope.row.is_refund==1"
                      style="display: block; color: #3363ff; cursor: pointer"
                    >
                      已退款
                    </span>
                  </div>
                  <el-tag v-if="scope.row.state==5" size="medium" type="info">
                    已取消
                  </el-tag>
                </template>
              </el-table-column> -->
            </el-table>
            <div class="block" v-if="allCount>0 && orderTable.length>0">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-size="limit"
                :current-page="currentPage"
                layout="total,sizes, prev, pager, next, jumper"
                :total="allCount"
              ></el-pagination>
            </div>
          </div>
          <div v-if="typePopover" class="popover-mask"></div>
        </div>
        <div
          class="order-container"
          v-if="isActive==1"
          style="width: calc(100% - 100px)"
        >
          <div class="table-wrap bg-white" style="width: 100%">
            <div class="order-search">
              <div
                style="
                  display: flex;
                  margin-bottom: 10px;
                  flex-direction: row;
                  justify-content: space-between;
                "
              >
                <el-select
                  v-model="refundOptionsValue"
                  @change="changeRefundOptions"
                >
                  <el-option
                    v-for="item in refundOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div class="block">
                  <span>申请时间：</span>
                  <el-date-picker
                    type="daterange"
                    v-model="refundDateRange"
                    value-format="yyyy-MM-dd"
                    @change="cahngeRefundDateRange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  ></el-date-picker>
                </div>
              </div>
              <div class="order-search">
                <el-input
                  placeholder="请输入订单编号"
                  suffix-icon="el-icon-search"
                  @keyup.enter.exact.native.stop="handleRefundSearch"
                  v-model.trim="refundKeyNum"
                ></el-input>
              </div>
            </div>

            <el-table
              v-loading="loadingRefundList"
              :data="refundList"
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 1)"
              class="orderTable"
              :header-cell-style="headerClass"
              ref="refundList"
            >
              <el-table-column label="申请人" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <p
                    v-if="scope.row.memberInfo && scope.row.memberInfo.member_name"
                  >
                    {{scope.row.memberInfo.member_name}}
                  </p>
                  <p
                    v-if="scope.row.memberInfo && scope.row.memberInfo.phone"
                    class="table_date"
                  >
                    {{scope.row.memberInfo.phone}}
                  </p>
                </template>
              </el-table-column>
              <el-table-column
                prop="order_number"
                label="订单编号/申请时间"
                width="240"
              >
                <template slot-scope="scope">
                  <p v-if="scope.row.order_number">
                    {{scope.row.order_number}}
                  </p>
                  <p v-if="scope.row.addtime" class="table_date">
                    {{scope.row.addtime}}
                  </p>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型">
                <template slot-scope="scope">
                  <span v-show="scope.row.type==1">仅退款</span>
                  <span v-show="scope.row.type==2">退款退货</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="money"
                label="退款金额/原因"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <p v-if="scope.row.money">
                    {{scope.row.money | filterMoney}}
                  </p>
                  <p>{{scope.row.reason || 无}}</p>
                </template>
              </el-table-column>
              <el-table-column
                prop="storeName"
                label="下单门店"
              ></el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status==1">审核通过</span>
                  <span v-if="scope.row.status==2">未审核</span>
                  <span v-if="scope.row.status==3">已拒绝</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <span
                    style="color: #3363ff; cursor: pointer"
                    v-if="scope.row.status==1 || scope.row.status==3"
                    @click="getRefundDetail(scope.row)"
                  >
                    查看
                  </span>
                  <span
                    style="color: #3363ff; cursor: pointer"
                    v-if="scope.row.status==2"
                    @click="getRefundDetail(scope.row)"
                  >
                    审核
                  </span>
                  <span
                    style="color: #3363ff; cursor: pointer"
                    @click="seeRefundDetail(scope.row)"
                  >
                    订单详情
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <div class="block" v-if="refundListCount>0 && refundList.length>0">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleRefundCurrentChange"
                :page-size="limit"
                :current-page="currentPage"
                layout="total,sizes, prev, pager, next, jumper"
                :total="refundListCount"
              ></el-pagination>
            </div>
          </div>
          <div v-if="typePopover" class="popover-mask"></div>
        </div>
      </div>

      <el-dialog
        title="订单详情"
        :show-close="false"
        class="order-details"
        :visible.sync="isDetails"
        :fullscreen="true"
      >
        <div slot="title">
          <app-subject
            title="订单详情"
            @return-title="bindCancelDetails"
          ></app-subject>
          <template v-if="orderDetails.is_refund==2">
            <template
              v-if="orderDetails.type==1 || orderDetails.type==2 ||orderDetails.type==3 ||orderDetails.type==5 || 1"
            >
              <!--1服务 2产品（1,2品项） 3售卡 4充值 5充卡 6 直接收款-->
              <template
                v-if="(orderDetails.state != 1) && (orderDetails.state != 5)"
              >
                <div
                  style="
                    color: #3363ff;
                    cursor: pointer;
                    position: absolute;
                    right: 0;
                    top: 0;
                    height: 50px;
                    line-height: 12px;
                  "
                  class="orderBtn"
                  @click="activeRefund"
                >
                  主动退款
                </div>
              </template>
            </template>
          </template>
        </div>
        <div
          class="order-content"
          v-loading="loading3"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255,255,255,0)"
        >
          <div class="contner-userInfo">
            <div class="order-info">
              <app-heading title="订单信息"></app-heading>
              <ul class="order-status">
                <li class="status-li" v-if="orderDetails.state==1">待付款</li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==2 && orderDetails.is_refund==2"
                >
                  待收货
                </li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==2 && orderDetails.is_refund==1"
                >
                  已退款
                </li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==3 && orderDetails.is_refund==2"
                >
                  已发货
                </li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==3 && orderDetails.is_refund==1"
                >
                  已退款
                </li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==4 && orderDetails.is_refund==2"
                >
                  已完成
                </li>
                <li
                  class="status-li"
                  v-if="orderDetails.state==4 && orderDetails.is_refund==1"
                >
                  已退款
                </li>
                <li class="status-li" v-if="orderDetails.state==5">已取消</li>
              </ul>
              <!-- 到店 订单编号 下单 收款 其他属于 预约 -->
              <ol class="order-date">
                <!--<li class="order-date-li" v-if="reservation.number">-->
                <!--<span class="order-label">预约单号</span>-->
                <!--<span>{{reservation.number}}</span>-->
                <!--</li>-->
                <!--<li class="order-date-li" v-if="reservation.time">-->
                <!--<span class="order-label">预约时间</span>-->
                <!--<span>{{reservation.time}}</span>-->
                <!--</li>-->
                <!--<li class="order-date-li" v-if="reservation.people">-->
                <!--<span class="order-label">到店人</span>-->
                <!--<span>{{reservation.people}}</span>-->
                <!--</li>-->
                <li class="order-date-li">
                  <span class="order-label">订单编号</span>
                  <span>{{orderDetails.order_number}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">下单时间</span>
                  <span>{{orderDetails.order_time}}</span>
                </li>
                <li
                  class="order-date-li"
                  v-if="orderDetails.collection_time!=''"
                >
                  <span class="order-label">收款时间</span>
                  <span>{{orderDetails.collection_time}}</span>
                </li>
                <li
                  class="order-date-li"
                  v-if="orderDetails.time && orderDetails.state==3"
                >
                  <span class="order-label">发货时间</span>
                  <span>{{orderDetails.time}}</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.is_refund==1">
                  <span class="order-label">退款时间</span>
                  <span>{{orderDetails.refund_time}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">收银员</span>
                  <span>{{orderDetails.cashier}}</span>
                </li>
                <!-- <li class="order-date-li" v-if="orderDetails.state==2 && orderDetails.is_refund==1">
                <span class="order-label">退款时间</span>
                <span>{{orderDetails.refund_time}}</span>
            </li> -->
              </ol>
            </div>
            <div class="client-info" v-if="orderDetails.vip_id!=0">
              <app-heading title="客户信息"></app-heading>
              <!--<i class="iconfont icontouxiang"></i>-->
              <!-- !=0  -->
              <div
                class="details-vipImg"
                v-if="orderDetails.vip && orderDetails.vip && orderDetails.vip.pic"
              >
                <img :src="orderDetails.vip.pic" />
              </div>
              <p
                class="client-name"
                v-if="orderDetails.vip && orderDetails.vip.member_name"
              >
                {{orderDetails.vip.member_name}}
              </p>
              <p
                class="client-phone"
                v-if="orderDetails.vip && orderDetails.vip.phone"
              >
                {{orderDetails.vip.phone}}
              </p>
              <ul>
                <li class="order-date-li">
                  <span class="order-label">会员编号</span>
                  <span
                    v-if="orderDetails.vip && orderDetails.vip.member_number"
                  >
                    {{orderDetails.vip.member_number}}
                  </span>
                </li>
                <!-- //TODO 返回值添加is_vip字段 -->
                <!-- <li
                  class="order-date-li"
                  v-if="orderDetails.vip && orderDetails.vip.Grade"
                >
                  <span class="order-label">会员等级</span>
                  <span>{{orderDetails.vip.Grade}}</span>
                </li> -->
              </ul>
            </div>
          </div>

          <div class="content-product">
            <app-heading title="消费明细"></app-heading>
            <div class="product-list">
              <ul>
                <li
                  v-for="(item,index) in orderDetails.orderInfo"
                  class="orderDetails-li"
                >
                  <p class="serialNumber">{{index + 1}}、</p>
                  <div class="details-list">
                    <div class="order-date-li">
                      <span class="order-label">
                        <span>{{item.name}}</span>
                        <span v-if="item.sku_name">| {{item.sku_name}}</span>
                      </span>
                      <span>￥{{item.price}}</span>
                    </div>
                    <div class="order-date-li">
                      <span class="order-label">数量</span>
                      <span>×{{item.num}}</span>
                    </div>
                    <div class="order-date-li" v-if="item.equity_type!=1">
                      <span class="order-label" v-if="item.equity_type==3">
                        次卡抵扣
                      </span>
                      <span class="order-label" v-if="item.equity_type==2">
                        充值卡折扣
                      </span>
                      <span v-if="item.equity_type!=4">
                        -￥{{item.reduceprice | formatMark}}
                      </span>
                    </div>
                    <div class="order-date-li" v-if="item.equity_type==4">
                      <span class="order-label" v-if="item.equity_type==4">
                        优惠金额
                      </span>
                      <span v-if="item.reduceprice.indexOf('-')==-1">
                        +￥{{item.reduceprice | formatMark}}
                      </span>
                      <span v-if="item.reduceprice.indexOf('-')!=-1">
                        -￥{{item.reduceprice | formatMark}}
                      </span>
                    </div>
                    <div class="order-date-li">
                      <span class="order-label">小计</span>
                      <span>￥{{item.Subtotal}}</span>
                    </div>
                    <!--预约订单显示-->
                    <div class="order-date-li">
                      <span class="order-label">
                        选择
                        <technician-name></technician-name>
                      </span>
                      <span>{{item.Craftsman}}</span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <app-heading
              v-if="orderDetails.presentData && orderDetails.presentData.length>0"
              title="赠送明细"
            ></app-heading>
            <div class="product-list">
              <ul>
                <li
                  v-for="(item,index) in orderDetails.presentData"
                  class="orderDetails-li"
                >
                  <p class="serialNumber">{{index + 1}}、</p>
                  <div class="details-list">
                    <div class="order-date-li">
                      <span class="order-label">
                        <span v-if="item.itemType==1">
                          {{item.name}}（服务）
                        </span>
                        <span v-if="item.itemType==2 && !item.sku_name">
                          {{item.name}}（产品）
                        </span>
                        <span v-if="item.itemType==2 && item.sku_name">
                          {{item.name}}
                        </span>
                        <span v-if="item.sku_name">
                          | {{item.sku_name}}（产品）
                        </span>
                      </span>
                      <span>￥{{item.price | filterMoney}}</span>
                    </div>
                    <div class="order-date-li">
                      <span class="order-label">数量</span>
                      <span>×{{item.num}}</span>
                    </div>
                    <div class="order-date-li">
                      <span class="order-label">赠送状态</span>
                      <span v-if="item.status==0">仅选择</span>
                      <span v-if="item.status==1">已赠送</span>
                      <span v-if="item.status==2">已退回</span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div class="product-userInfo" style="margin-top: 15px">
              <!--预约 只显示 合计 付款 合计收款-->
              <ul
                style="border-bottom: 1px solid #e5e5e5"
                v-if="addressInfoFlag && orderDetails.address_info && orderDetails.address_info.id!=0"
              >
                <li class="order-date-li">
                  <span class="order-label">收货人</span>
                  <span>{{orderDetails.address_info.name}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">收货电话</span>
                  <span>{{orderDetails.address_info.tel}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">收货地址</span>
                  <span>{{orderDetails.address_info.address}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">运费</span>
                  <span v-if="orderDetails.dispatch_fee!='0.00'">
                    ￥{{orderDetails.dispatch_fee}}
                  </span>
                  <span v-if="orderDetails.dispatch_fee=='0.00'">无</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.logistics">
                  <span class="order-label">物流公司</span>
                  <span>{{orderDetails.logistics}}</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.courier_num">
                  <span class="order-label">物流单号</span>
                  <span>{{orderDetails.courier_num}}</span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">备注</span>
                  <span v-if="orderDetails.remarks!=''">
                    {{orderDetails.remarks}}
                  </span>
                  <span v-if="orderDetails.remarks==''">无</span>
                </li>
              </ul>
              <ul>
                <!--<li class="order-date-li">-->
                <!--<span class="order-label">合计</span>-->
                <!--<span>暂无</span>-->
                <!--</li>-->
                <li class="order-date-li" v-if="orderDetails.summation">
                  <span class="order-label">合计</span>
                  <span>￥{{orderDetails.summation | filterMoney}}</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.dismoney!='0.00'">
                  <span class="order-label">充值卡折扣</span>
                  <span>-￥{{orderDetails.dismoney}}</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.deduction!='0.00'">
                  <span class="order-label">次卡抵扣</span>
                  <span>-￥{{orderDetails.deduction}}</span>
                </li>
                <li
                  class="order-date-li"
                  v-if="orderDetails.member_counpon_money"
                >
                  <span class="order-label">优惠券</span>
                  <span>
                    -￥{{orderDetails.member_counpon_money | filterMoney}}
                  </span>
                </li>
                <li class="order-date-li" v-if="orderDetails.manuallys!='0.00'">
                  <span class="order-label">优惠金额</span>
                  <span v-if="orderDetails.manually>0">
                    -￥{{orderDetails.manuallys}}
                  </span>
                  <span v-if="orderDetails.manually<0">
                    +￥{{orderDetails.manuallys}}
                  </span>
                  <span v-if="orderDetails.manually==0">
                    ￥{{orderDetails.manuallys}}
                  </span>
                </li>
                <li
                  class="order-date-li"
                  v-if="orderDetails.small_change_money"
                >
                  <span class="order-label">抹零</span>
                  <span>
                    -￥{{orderDetails.small_change_money | filterMoney}}
                  </span>
                </li>
                <li class="order-date-li" v-if="orderDetails.dispatch_type==2">
                  <span class="order-label">运费</span>
                  <span>￥{{orderDetails.dispatch_fee}}</span>
                </li>
                <li class="order-date-li" v-if="orderDetails.is_debt==1">
                  <span class="order-label" style="color: red">欠账</span>
                  <span style="color: red">
                    ￥{{orderDetails.debt_value | filterMoney}}
                  </span>
                </li>
                <li class="order-date-li">
                  <span class="order-label">合计收款</span>
                  <span>￥{{orderDetails.receivable}}</span>
                </li>
                <!--<li class="order-date-li">-->
                <!--<span class="order-label">合计收款</span>-->
                <!--<span>暂无</span>-->
                <!--</li>-->
                <!-- <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.name">
                <span class="order-label">收货人</span>
                <span>{{orderDetails.address_info.name}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.tel">
                <span class="order-label">收货电话</span>
                <span>{{orderDetails.address_info.tel}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.address">
                <span class="order-label">收货地址</span>
                <span>{{orderDetails.address_info.address}}</span>
            </li>
            <li class="order-date-li"  v-if="orderDetails.state==3&&orderDetails.address_info">
                <span class="order-label">物流公司</span>
                <span>{{orderDetails.logisticsName}}</span>
            </li>
            <li class="order-date-li"  v-if="orderDetails.state==3&&orderDetails.address_info">
                <span class="order-label">快递单号</span>
                <span>{{orderDetails.courier_num}}</span>
            </li> -->
              </ul>
            </div>
            <!--已取消  none -->
          </div>
          <div class="details-btn">
            <p style="box-sizing: border-box; padding: 20px 0">
              合计收款：￥{{orderDetails.receivable}}
            </p>
            <!--1待付款 2待收货 3 已发货 4 已完成  5 已取消-->
            <div style="display: flex">
              <!-- <template v-if="orderDetails.is_refund==2">
              <template
                      v-if="orderDetails.type==1 || orderDetails.type==2 ||orderDetails.type==3 ||orderDetails.type==5">
                  1服务 2产品（1,2品项） 3售卡 4充值 5充卡 6 直接收款
                  <template v-if="(orderDetails.state != 1) && (orderDetails.state != 5)">
                      <div style="background:#f56c6c;color: #FFF;cursor: pointer;" class="orderBtn" @click="activeRefund">主动退款</div>
                  </template>
              </template>
          </template> -->
              <div
                v-if="orderDetails.is_debt==1"
                class="orderBtn"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
                @click="rePayOrder"
              >
                还款
              </div>
              <div
                v-if="orderDetails.state!=5"
                class="orderBtn print"
                @click="bindPrint"
                style="background: #ddd; cursor: pointer"
              >
                打印小票
              </div>
              <!-- <div
                v-if="loginModify == 1&&orderDetails.state==4&&orderDetails.type !=4&&orderDetails.type !=6"
                class="orderBtn print"
                @click="modifyEmployeePerformance"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
              >
                员工业绩
              </div> -->
              <div
                v-if="loginModify == 1&&orderDetails.state==4"
                class="orderBtn print"
                @click="modifyEmployeePerformance"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
              >
                员工业绩
              </div>
              <div
                class="orderBtn"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
                v-if="orderDetails.state == 2&&orderDetails.dispatch_type == 2 && orderDetails.is_refund==2"
                @click="deliverGoods"
              >
                发货
              </div>
              <div
                class="orderBtn"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
                v-if="orderDetails.state == 2&&orderDetails.dispatch_type == 1 && orderDetails.is_refund==2"
                @click="StoreGoods"
              >
                自提
              </div>
              <!--已完成block -->
              <!--<p class="orderBtn change">修改员工业绩</p>-->
              <div
                v-if="orderDetails.state==1"
                class="orderBtn"
                style="background: #999; color: #fff; cursor: pointer"
                @click="bindCancelOrder"
              >
                取消订单
              </div>
              <div
                v-if="(orderDetails.type==6 || orderDetails.type==4 || orderDetails.type==5) && orderDetails.state=='1'"
                class="orderBtn"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
                @click="bindDirectPay()"
              >
                收款
              </div>
              <div
                v-else-if="orderDetails.state=='1'"
                class="orderBtn"
                style="background: #3363ff; color: #ffffff; cursor: pointer"
                @click="bindModifyOrder()"
              >
                开单收款
              </div>
              <!--<span>修改订单</span>-->
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 修改员工业绩 -->
      <el-dialog
        :visible.sync="isModifyPerformance"
        fullscreen
        :show-close="true"
        class="cz_kaidan"
        append-to-body
      >
        <div class="kaidan">
          <div slot="title">
            <div class="cz_qudan_top" style="background: #f6f6f6; color: #333">
              <div style="flex: 1; text-align: center">修改员工业绩</div>
            </div>
          </div>
          <ul class="performance_list">
            <li
              v-for="(performanceList,performanceIndex) in performanceList"
              :key="'performanceIndex'+performanceIndex"
              class="performance_list_item"
            >
              <div class="bg-gray-100 flex space-x-3 rounded-md px-6 py-4">
                <div class="flex shrink-0 space-x-4 text-lg font-bold">
                  {{performanceIndex + 1}}
                </div>
                <div>
                  <div class="text-lg font-bold">{{performanceList.name}}</div>
                  <div class="mt-1 flex space-x-6">
                    <div>单价：{{performanceList.price}}</div>
                    <div>数量：{{performanceList.num}}</div>
                    <div>
                      优惠权益：
                      <span v-if="performanceList.equity_type == 1">
                        未使用权益
                      </span>
                      <span v-else-if="performanceList.equity_type == 2"
                        >折扣</span
                      >
                      <span v-else-if="performanceList.equity_type == 3"
                        >抵价</span
                      >
                      <span v-else="performanceList.equity_type == 4">
                        优惠金额
                      </span>
                    </div>
                    <div>实际收款：{{performanceList.Subtotal}}</div>
                  </div>
                </div>
              </div>
              <div
                v-show="zuhekaPerformance !=6||performanceList.salesmen.length != 0"
              >
                <ul class="performance_title">
                  <li style="width: 15%; text-align: center">角色</li>
                  <li style="width: 6%">#</li>
                  <li style="width: 16%">人员</li>
                  <li style="width: 15%">提成方式</li>
                  <li style="width: 25%">业绩金额</li>
                  <li style="width: 25%">提成金额</li>
                  <li style="width: 10%; text-align: center">操作</li>
                </ul>
                <div class="performance_row">
                  <div>
                    <el-button
                      type="text"
                      @click="chooseSales(performanceIndex)"
                    >
                      选择销售
                    </el-button>
                  </div>
                  <ul>
                    <li
                      v-for="(salesmen,index) in performanceList.salesmen"
                      :key="'salesmen'+index"
                      style="height: 40px; line-height: 40px"
                    >
                      <div style="width: 4%">{{index+1}}</div>
                      <div style="width: 14%">{{salesmen.staffName}}</div>
                      <div v-if="salesmen.deduct_way == 1" style="width: 10%">
                        比例提成
                      </div>
                      <div v-if="salesmen.deduct_way == 2" style="width: 10%">
                        固定提成
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="salesmen.performance"
                            @input="limitInputMoney($event,performanceIndex,index,true)"
                            type="number"
                            step=".01"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="salesmen.performance_proportion"
                            @input="limitInputPer($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="salesmen.commission"
                            @input="limitInputMoney1($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="salesmen.commission_proportion"
                            @input="limitInputPer1($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div style="width: 9%; text-align: center">
                        <span
                          @click="delectsalesmen(salesmen,index,performanceIndex)"
                          style="cursor: pointer"
                        >
                          删除
                        </span>
                      </div>
                    </li>
                    <li
                      style="height: 40px; line-height: 40px"
                      v-for="(newSalesmen,index) in performanceList.addSalesmen"
                      :key="'newSalesmen'+index"
                    >
                      <div style="width: 4%">{{newSalesmen.lengthh}}</div>
                      <div style="width: 14%">{{newSalesmen.staffName}}</div>
                      <div style="width: 10%">
                        <!-- <el-select
                          v-model="newSalesmen.deduct_way"
                          placeholder="请选择"
                          @change="chooseDeductType($event,index,performanceIndex)"
                          class="choose_deduct"
                        >
                          <el-option
                            v-for="item in deductType"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select> -->
                        自定义
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="newSalesmen.performance"
                            @input="limitInputMoneyAdd($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="newSalesmen.performance_proportion"
                            @input="limitInputPerAdd($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="newSalesmen.commission"
                            @input="limitInputMoneyAdd1($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="newSalesmen.commission_proportion"
                            @input="limitInputPerAdd1($event,performanceIndex,index,true)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div style="width: 9%"></div>
                    </li>
                  </ul>
                </div>
                <div class="performance_row" v-show="performanceList.type ==1">
                  <div>
                    <el-button
                      type="text"
                      @click="chooseCrafts(performanceList,performanceIndex)"
                    >
                      选择
                      <technician-name></technician-name>
                    </el-button>
                  </div>
                  <ul>
                    <li
                      v-for="(technicians,index) in performanceList.technicians"
                      style="height: 40px; line-height: 40px"
                      :key="'technicians'+index"
                    >
                      <div style="width: 4%">{{index+1}}</div>
                      <div style="width: 14%">
                        {{technicians.staffName}}
                        <el-button
                          type="info"
                          round
                          v-if="technicians.assign == 1"
                          size="mini"
                          style="padding: 4px 6px"
                        >
                          点客
                        </el-button>
                      </div>
                      <div
                        v-if="technicians.deduct_way == 1"
                        style="width: 10%"
                      >
                        比例提成
                      </div>
                      <div
                        v-if="technicians.deduct_way == 2"
                        style="width: 10%"
                      >
                        固定提成
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="technicians.performance"
                            @input="limitInputMoney($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="technicians.performance_proportion"
                            @input="limitInputPer($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="technicians.commission"
                            @input="limitInputMoney1($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="technicians.commission_proportion"
                            @input="limitInputPer1($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div style="width: 9%; text-align: center">
                        <span
                          @click="delectCrafts(technicians,index,performanceIndex)"
                          style="cursor: pointer"
                        >
                          删除
                        </span>
                      </div>
                    </li>
                    <li
                      style="height: 40px; line-height: 40px"
                      v-for="(items,index) in performanceList.addCrafts"
                      :key="'items'+index"
                    >
                      <div style="width: 4%">{{items.lengthh}}</div>
                      <div style="width: 14%">{{items.staffName}}</div>
                      <div style="width: 10%">
                        <!-- <el-select
                          v-model="items.deduct_way"
                          placeholder="请选择"
                          @change="chooseDeductType($event,index,performanceIndex)"
                          class="choose_deduct"
                        >
                          <el-option
                            v-for="item in deductType"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select> -->
                        自定义
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="items.performance"
                            @input="limitInputMoneyAdd($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="items.performance_proportion"
                            @input="limitInputPerAdd($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div class="performance_group_input" style="width: 25%">
                        <div class="input-group">
                          <input
                            v-model="items.commission"
                            @input="limitInputMoneyAdd1($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">元</div>
                        </div>
                        <div class="input-group">
                          <input
                            v-model="items.commission_proportion"
                            @input="limitInputPerAdd1($event,performanceIndex,index,false)"
                            type="number"
                            class="native-input"
                          />
                          <div class="input-append">%</div>
                        </div>
                      </div>
                      <div style="width: 9%"></div>
                    </li>
                  </ul>
                </div>
              </div>
            </li>
          </ul>
          <span
            slot="footer"
            class="dialog-footer"
            style="display: block; text-align: center"
          >
            <el-button @click="isModifyPerformance = false">取 消</el-button>
            <el-button type="primary" @click="saveModify">保 存</el-button>
          </span>
        </div>
      </el-dialog>

      <!-- 选择销售弹框 -->
      <el-dialog
        title="选择人员"
        :visible.sync="isSales"
        width="40%"
        class="kd_add_box"
      >
        <div class="sales_content">
          <el-checkbox-group
            v-for="(items,index) in AllSales"
            :key="index"
            v-model="salesChecked"
          >
            <el-checkbox
              :label="index"
              border
              :disabled="items.isDisabled?true:false"
            >
              {{items.nickname}}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isSales = false">取 消</el-button>
          <el-button type="primary" @click="addSalesmen">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 选择<technician-name></technician-name>弹框 -->
      <el-dialog
        title="选择人员"
        :visible.sync="isCrafts"
        width="40%"
        class="kd_add_box"
      >
        <div class="sales_content">
          <el-checkbox-group
            v-for="(items,index) in AllCrafts"
            :key="index"
            v-model="craftsChecked"
          >
            <el-checkbox
              :label="index"
              border
              :disabled="items.isDisabled?true:false"
            >
              {{items.nickname}}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isCrafts = false">取 消</el-button>
          <el-button type="primary" @click="addCrafts">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 发货弹框 -->
      <el-dialog
        title="发货"
        :visible.sync="isdeliverBox"
        append-to-body
        width="600px"
        @close="closeReceiptBox"
        class="deliver_box"
      >
        <el-table :data="orderDetails.orderInfo">
          <el-table-column label="图片" width="150">
            <template slot-scope="scope">
              <div>
                <img :src="scope.row.imgUrl" alt="" style="width: 60px" />
              </div>
            </template>
          </el-table-column>
          <el-table-column property="name" label="商品"></el-table-column>
          <el-table-column property="price" label="单价"></el-table-column>
          <el-table-column property="num" label="数量"></el-table-column>
        </el-table>
        <div class="receipt_information">
          <ul>
            <li>
              <div class="receipt_before">收货人</div>
              <div>{{addressInfo.name}}</div>
            </li>
            <li>
              <div class="receipt_before">收货电话</div>
              <div>{{addressInfo.tel}}</div>
            </li>
            <li>
              <div class="receipt_before">收货地址</div>
              <div>{{addressInfo.address}}</div>
            </li>
            <li class="nologisyic">
              <div class="receipt_before">无需物流</div>
              <el-switch
                v-model="value"
                active-color="gray"
                inactive-color="black"
                @change="changeStatus"
              ></el-switch>
            </li>
          </ul>
          <div class="choose_logisyic" v-if="value">
            <div>
              <div class="receipt_before">物流单号</div>
              <el-input
                v-model="logisticsNum"
                placeholder="请输入物流单号"
                class="logistic_input"
              ></el-input>
            </div>
            <div>
              <div class="receipt_before">物流公司</div>
              <el-select
                v-model="companyName"
                placeholder="请选择"
                class="logistic_input"
                @change="chooseCompany"
              >
                <el-option
                  v-for="item in logisticCompanys"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isdeliverBox = false">取 消</el-button>
          <el-button type="primary" @click="sendGoods">确 定</el-button>
        </span>
      </el-dialog>
      <!-- <technician-name></technician-name>代收 -->
      <el-dialog
        :visible.sync="isStaffCollect"
        append-to-body
        width="600px"
        @close="closeReceiptBox"
        class="deliver_box"
      >
        <template slot="title">
          <technician-name></technician-name>
          代收
        </template>
        <div class="receipt_information">
          <ul>
            <li>
              <div class="receipt_before">
                代收
                <technician-name></technician-name>
                :
              </div>
              <div>{{staffCollectData.staffName}}</div>
            </li>
            <li>
              <div class="receipt_before">代收金额:</div>
              <div>￥{{staffCollectData.trade_amount}}元</div>
            </li>
            <li>
              <div class="receipt_before" v-if="staffCollectData.pay_type">
                代收方式:
              </div>
              <div v-if="staffCollectData.pay_type==1">微信</div>
              <div v-if="staffCollectData.pay_type==2">支付宝</div>
              <div v-if="staffCollectData.pay_type==3">现金</div>
            </li>
          </ul>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelStaffCollect">取 消</el-button>
          <el-button type="primary" @click="confirmReceipt">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 自提弹框 -->
      <el-dialog
        title="自提"
        :visible.sync="isStoreBox"
        append-to-body
        width="600px"
        @close="closeReceiptBox"
        class="deliver_box"
      >
        <el-table :data="orderDetails.orderInfo">
          <el-table-column label="图片" width="150">
            <template slot-scope="scope">
              <div>
                <img :src="scope.row.imgUrl" alt="" style="width: 60px" />
              </div>
            </template>
          </el-table-column>
          <el-table-column property="name" label="商品"></el-table-column>
          <el-table-column property="price" label="单价"></el-table-column>
          <el-table-column property="num" label="数量"></el-table-column>
        </el-table>
        <div class="receipt_information">
          <ul>
            <li>
              <div class="receipt_before">取件人</div>
              <div>{{addressInfo.name}}</div>
            </li>
            <li>
              <div class="receipt_before">取件人电话</div>
              <div>{{addressInfo.phoneNumber}}</div>
            </li>
          </ul>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isStoreBox = false">取 消</el-button>
          <el-button type="primary" @click="sendGoods">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 退款申请列表 -->
      <el-dialog
        title="申请信息"
        :visible.sync="isApply"
        append-to-body
        width="800px"
      >
        <div style="width: 100%; border-top: 1px solid #ccc">
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div v-if="refundApplyData.memberInfo">
                申请人：{{refundApplyData.memberInfo.member_name}}
              </div>
            </el-col>
            <el-col :span="12">
              <div v-if="refundApplyData.memberInfo">
                申请人电话：{{refundApplyData.memberInfo.phone}}
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div v-if="refundApplyData.type==1">申请类型：仅退款</div>
              <div v-if="refundApplyData.type==2">申请类型：退货退款</div>
            </el-col>
            <el-col :span="12">
              <div v-if="refundApplyData.status==1">审核状态：审核通过</div>
              <div v-if="refundApplyData.status==2">审核状态：未审核</div>
              <div v-if="refundApplyData.status==3">审核状态：已拒绝</div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div>商品金额：￥{{refundApplyData.money}}</div>
            </el-col>
            <el-col :span="12">
              <div>退款总额：￥{{refundApplyData.total}}</div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div>邮费：￥{{refundApplyData.postage}}</div>
            </el-col>
            <el-col :span="12">
              <div v-if="refundApplyData.goods_status==1">
                商品状态：货已收到
              </div>
              <div v-if="refundApplyData.goods_status==2">
                商品状态：未收到货
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div>退款原因：{{refundApplyData.reason || '暂无'}}</div>
            </el-col>
            <el-col :span="12">
              <div
                :title="refundApplyData.remarks"
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                退款说明：{{refundApplyData.remarks || '暂无'}}
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div>申请时间：{{refundApplyData.addtime}}</div>
            </el-col>
            <el-col :span="12">
              <div></div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="refundApply">
            <el-col :offset="2" :span="10">
              <div>
                图片内容：
                <el-image
                  class="elementImage"
                  v-if="refundApplyData.imgArr"
                  style="width: 100px; height: 100px"
                  :src="refundApplyData.imgArr[0]"
                  :preview-src-list="refundApplyData.imgArr"
                  fit="cover"
                ></el-image>
              </div>
            </el-col>
            <el-col :span="12">
              <div
                class="el-row"
                v-if="refundApplyData.status==2"
                style="margin-bottom: 10px"
              >
                <span class="el-col el-col-5" style="line-height: 32px">
                  审核意见：
                </span>
                <div class="el-col el-col-14">
                  <el-input
                    size="medium"
                    v-model="refundApplyData.refuse_reason"
                    palceholder="审核意见"
                  ></el-input>
                </div>
              </div>
              <div
                v-else
                :title="refundApplyData.refuse_reason"
                style="
                  margin-bottom: 10px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                审核意见：{{refundApplyData.refuse_reason || "暂无"}}
              </div>
              <el-button
                type="primary"
                v-if="refundApplyData.status==2"
                @click="seeRefundDetail(refundApplyData)"
              >
                点击退款
              </el-button>
              <el-button
                type="danger"
                v-if="refundApplyData.status==2"
                @click="refusedRefund"
              >
                驳回退款
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>

      <!-- 退款商品信息 -->
      <el-dialog
        title="退款商品"
        :visible.sync="isRefundGoods"
        top="5vh"
        width="700px"
      >
        <el-table
          v-if="deductionArr && deductionArr.length>0"
          :data="deductionArr"
          :header-cell-style="headerClass"
          class="refundGoods"
          ref="deductionArr"
        >
          <el-table-column
            label="商品"
            :show-overflow-tooltip="true"
            width="320px"
          >
            <template slot-scope="scope">
              <div style="display: flex">
                <span v-if="scope.row.img">
                  <el-image
                    style="width: 36px; height: 36px"
                    :src="scope.row.imgUrl"
                    fit="cover"
                  ></el-image>
                </span>
                <div
                  style="margin-left: 10px; height: 36px; line-height: 36px"
                  v-show="scope.row.name"
                >
                  {{scope.row.name}}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="num"
            label="数量"
            width="170px"
          ></el-table-column>
          <el-table-column label="回退" width="170px">
            <template slot-scope="scope">
              <span>会员卡抵扣{{scope.row.num}}次</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="amountArr && amountArr.length>0"
          :data="amountArr"
          :header-cell-style="headerClass"
          class="refundGoods"
          ref="amountArr"
        >
          <el-table-column
            label="商品"
            :show-overflow-tooltip="true"
            width="320px"
          >
            <template slot-scope="scope">
              <div style="display: flex">
                <span v-if="scope.row.img">
                  <el-image
                    style="width: 36px; height: 36px"
                    :src="scope.row.imgUrl"
                    fit="cover"
                  ></el-image>
                </span>
                <div
                  style="margin-left: 10px; height: 36px; line-height: 36px"
                  v-show="scope.row.name"
                >
                  {{scope.row.name}}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="num"
            label="数量"
            width="170px"
          ></el-table-column>
          <el-table-column
            prop="Subtotal"
            label="单价"
            width="170px"
          ></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button
            v-if="orderDetails.type==3 || orderDetails.type==5"
            type="primary"
            @click="activeRefundCard"
          >
            查看卡项详情
          </el-button>
          <el-button v-else type="primary" @click="nextRefund">
            下一步
          </el-button>
        </span>
      </el-dialog>

      <!-- 退款卡项信息，查看卡项 -->
      <el-dialog
        title="卡项详情"
        :visible.sync="isRefundCard"
        top="5vh"
        width="700px"
      >
        <div style="border: 1px solid #ebeef5">
          <!-- <div style="margin: 10px 0 10px 10px;">卡项详情</div> -->
          <div class="refundheader">
            <el-row :gutter="20" class="refundApply">
              <el-col :offset="2" :span="10">
                <div>卡项名称：{{refundCardData.card_name}}</div>
              </el-col>
              <el-col :span="12">
                <div
                  v-if="refundCardData.cardtype==1 && refundCardData.cardid==0"
                >
                  卡项类型：充卡
                </div>
                <div v-if="refundCardData.cardtype==2">卡项类型：充值卡</div>
                <div
                  v-if="refundCardData.cardtype==1 && refundCardData.cardid>0"
                >
                  卡项类型：次卡
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="refundApply">
              <el-col :offset="2" :span="10">
                <div>有效期：{{refundCardData.indateName}}</div>
              </el-col>
              <el-col :span="12">
                <div>购卡时间：{{refundCardData.buyTimeName}}</div>
              </el-col>
            </el-row>
            <el-row
              :gutter="20"
              class="refundApply"
              v-if="refundCardData.cardtype==2"
            >
              <el-col :offset="2" :span="10">
                <div>
                  总金额：￥{{refundCardData.totalbalance | filterMoney}}
                </div>
              </el-col>
              <el-col :span="12">
                <div>已使用：￥{{refundCardData.usebalance | filterMoney}}</div>
              </el-col>
            </el-row>
            <el-row
              :gutter="20"
              class="refundApply"
              v-if="refundCardData.cardtype==2"
            >
              <el-col :offset="2" :span="10">
                <div>
                  剩余总额：￥{{refundCardData.residuebalance | filterMoney}}
                </div>
              </el-col>
              <el-col :span="12">
                <div>
                  剩余本金：￥{{refundCardData.capitalbalance | filterMoney}}
                </div>
              </el-col>
            </el-row>
            <el-row
              :gutter="20"
              class="refundApply"
              v-if="refundCardData.cardtype==2"
            >
              <el-col :offset="2" :span="10">
                <div>
                  剩余赠额：￥{{refundCardData.presentbalance | filterMoney}}
                </div>
              </el-col>
              <!-- <el-col :span="12" >
              <div >剩余本金：{{refundCardData.buyTimeName}}</div>
          </el-col> -->
            </el-row>
          </div>
          <el-table
            v-if="refundCardData && refundCardData.cardDetail"
            :data="refundCardData.cardDetail"
            :header-cell-style="headerClass"
            class="refundCardData"
            ref="refundCardData"
          >
            <el-table-column
              label="服务"
              :show-overflow-tooltip="true"
              width="310px"
            >
              <template slot-scope="scope">
                <div
                  style="margin-left: 10px; height: 36px; line-height: 36px"
                  v-if="scope.row.goodsInfo"
                >
                  {{scope.row.goodsInfo.service_name}}
                  <span
                    v-if="scope.row.isgive==1"
                    style="color: #fff; background: #e74c75; padding: 0 2px"
                  >
                    赠
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="权益内容">
              <template slot-scope="scope">
                <div style="box-sizing: border-box; padding-right: 20px">
                  <div v-if="scope.row.isgive==1">{{scope.row.num}}次</div>
                  <div
                    v-if="scope.row.isgive==2 && scope.row.card_type==1 && scope.row.once_cardtype==1"
                  >
                    {{scope.row.num}}次
                  </div>
                  <div
                    v-if="scope.row.isgive==2 && scope.row.card_type==1 && scope.row.once_cardtype==2"
                  >
                    不限次
                  </div>
                  <div
                    v-if="scope.row.isgive==2 && scope.row.card_type==1
                                && scope.row.once_cardtype==3 &&
                                (scope.$index==refundCardData.cardDetail.length-1 ||(refundCardData.cardDetail[(scope.$index+1)]['isgive']==1))"
                  >
                    以上服务共{{scope.row.num}}次
                  </div>
                  <div v-if="scope.row.isgive==2 && scope.row.card_type==2">
                    {{scope.row.discount}}折
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="权益使用">
              <template slot-scope="scope">
                <div v-if="scope.row.card_type==2 && scope.row.isgive!=1">
                  --
                </div>
                <div v-if="scope.row.card_type==1 || scope.row.isgive==1">
                  已使用{{scope.row.usenum}}次
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isRefundCard=false">关闭</el-button>
          <el-button type="primary" @click="nextRefund">下一步</el-button>
        </span>
      </el-dialog>

      <!-- 退款信息 主动退款 -->
      <el-dialog
        title="退款商品"
        :visible.sync="isRefundMoney"
        top="5vh"
        width="700px"
      >
        <el-form
          ref="refundMoneyForm"
          :model="refundMoneyForm"
          label-width="100px"
          class="refundForm"
        >
          <el-form-item
            v-if="deductionNum"
            style="margin-bottom: 8px"
            label="抵扣卡回退"
          >
            <span>{{deductionNum}}次</span>
          </el-form-item>
          <el-alert
            title="该笔订单的权益抵扣将返还给顾客。"
            type="warning"
            v-if="deductionNum"
            style="margin-bottom: 22px"
            :closable="false"
          ></el-alert>
          <template v-if="orderDetails.member_coupon">
            <el-form-item style="margin-bottom: 8px" label="优惠券">
              <el-radio-group v-model="refundCoupon">
                <el-radio :label="0">不退回</el-radio>
                <el-radio :label="1">退回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-alert
              title="该笔订单使用的优惠券是否退还给顾客。"
              type="warning"
              style="margin-bottom: 22px"
              :closable="false"
            ></el-alert>
          </template>
          <template v-if="refundPayment" v-for="(item,key) in refundPayment">
            <el-form-item label="最多可退">
              <el-input
                disabled
                style="width: 50%"
                v-model="item.trade_amount"
              ></el-input>
            </el-form-item>
            <el-form-item label="退款金额">
              <el-input
                style="width: 50%"
                v-model="item.repayMoney"
                @focus="focusMoney"
                @input="changeRefundMoney(key,item.repayMoney)"
              ></el-input>
            </el-form-item>
            <el-form-item label="退款方式" style="margin-bottom: 8px">
              <el-radio-group
                v-model="item.refundMode"
                @change="changeRefundType"
              >
                <el-radio :label="1">现金退款</el-radio>
                <el-radio :label="0">原路退回 ({{item.payTypeName}})</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-alert
              v-if="item.refundMode==1"
              title="该笔订单的付款将以现金的方式退款给顾客，系统仅做退款记账。"
              type="warning"
              style="margin-bottom: 22px"
              :closable="false"
            ></el-alert>
            <el-alert
              v-else
              title="该笔订单按付款方式，原路退回到顾客账户。"
              type="warning"
              style="margin-bottom: 22px"
              :closable="false"
            ></el-alert>
          </template>
          <template
            v-if="orderDetails.presentData && orderDetails.presentData.length>0"
          >
            <el-form-item style="margin-bottom: 8px" label="赠送退回">
              <el-radio-group v-model="presentRefund">
                <el-radio :label="1">退回</el-radio>
                <el-radio :label="0">不退回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-alert
              title="无论赠送的产品(或服务)是否退回，店铺的赠送额度不会因此发生变化。"
              type="warning"
              style="margin-bottom: 22px"
              :closable="false"
            ></el-alert>
          </template>
          <el-form-item label="备注">
            <el-input
              type="textarea"
              style="width: 65%"
              v-model="refundRemarks"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <div style="float: left">
            <div v-if="deductionNum">
              抵扣卡回退{{deductionNum}}次
              <span v-if="istbz">（获客权益不退回）</span>
            </div>
            <div>
              合计退款:
              <span style="color: #ff0000">
                ￥{{repayTotalMoney | filterMoney}}
              </span>
            </div>
          </div>
          <el-button @click="cancelRefundMoney">取消</el-button>
          <el-button type="primary" @click="checkRefund">退款</el-button>
        </span>
      </el-dialog>

      <!-- 确认退款，密码弹框 -->
      <el-dialog
        title="确认密码"
        :visible.sync="isRefundPassword"
        :before-close="cancelComfirmRefund"
        width="560px"
      >
        <el-row :gutter="20">
          <el-col :span="5">
            <div style="height: 40px; line-height: 40px">请输入密码：</div>
          </el-col>
          <el-col :span="14">
            <el-input
              ref="refundMoneyPass"
              type="password"
              placeholder="请输入收银员密码"
              @keyup.enter.native="comfirmRefund"
              v-model="refundPassword"
            ></el-input>
          </el-col>
        </el-row>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelComfirmRefund">取消</el-button>
          <el-button type="primary" @click="comfirmRefund">确认退款</el-button>
        </span>
      </el-dialog>

      <el-dialog
        title="打印小票"
        :show-close="false"
        width="300px"
        top="45px"
        class="order-details"
        :visible.sync="isPrint"
        :append-to-body="true"
      >
        <div slot="title">
          <div class="print-title">
            <span class="print-cancel" @click="bindPrintCancen">取消</span>
            <span class="print-Text">打印小票</span>
            <span class="print-confirm" @click="bindPrintConfirm">确定</span>
          </div>
        </div>

        <div class="print-main">
          <template v-for="(item,index) in printSet.set">
            <template v-if="item.name=='store'">
              <app-store :store-set="item.set" :store="loginInfo"></app-store>
            </template>
            <template v-if="item.name=='header'">
              <app-header
                :header-set="item.set"
                :order-header="orderDetails"
              ></app-header>
            </template>
            <template v-if="item.name=='goods'">
              <app-goods
                :goods-set="item.set"
                :goods="orderDetails.orderInfo"
              ></app-goods>
            </template>
            <template v-if="item.name=='goods'">
              <app-gift
                :goods-set="item.set"
                :gift="orderDetails.presentData"
              ></app-gift>
            </template>
            <template v-if="item.name=='vip'">
              <app-vip
                :vip-set="item.set"
                :member="orderDetails.vip"
                :order-details="orderDetails"
                :print-type="2"
              ></app-vip>
            </template>
            <!-- <template v-if="item.name=='takegoods'">
            <app-address
              :address-set="item.set"
              :order-info="orderDetails"
            ></app-address>
          </template> -->
            <template v-if="item.name=='footer'">
              <app-footer
                :footer-set="item.set"
                :order-footer="orderDetails"
                :print-type="2"
              ></app-footer>
            </template>
            <template v-if="item.name=='line'">
              <app-line :line-set="item.set"></app-line>
            </template>
            <template v-if="item.name=='info'">
              <app-info :info-set="item.set"></app-info>
            </template>
            <template v-if="item.name=='text'">
              <app-text :text-set="item.set"></app-text>
            </template>
          </template>
        </div>

        <!-- <div class="print-main">
          <p class="store-name">{{orderDetails.storeTag}}</p>
          <div class="order-info">
            <app-heading title="订单信息"></app-heading>
            <ol class="order-date">
              <li class="order-date-li">
                <span class="order-label">订单类型</span>
                <span v-if="orderDetails.state==1">待付款</span>
                <span v-if="orderDetails.state==2">待收货</span>
                <span v-if="orderDetails.state==3">已发货</span>
                <span v-if="orderDetails.state==4">已完成</span>
                <span v-if="orderDetails.state==5">已取消</span>
              </li>
              <li class="order-date-li">
                <span class="order-label">订单编号</span>
                <span>{{orderDetails.order_number}}</span>
              </li>
              <li class="order-date-li">
                <span class="order-label">下单时间</span>
                <span>{{orderDetails.order_time}}</span>
              </li>
              <li class="order-date-li" v-if="orderDetails.vip_id==0">
                <span class="order-label">下单人</span>
                <span>普通客户</span>
              </li>
              <li class="order-date-li">
                <span class="order-label">收银员</span>
                <span>{{orderDetails.cashier}}</span>
              </li>
              <li class="order-date-li" v-if="orderDetails.state==4">
                <span class="order-label">收款时间</span>
                <span>{{orderDetails.collection_time}}</span>
              </li>
            </ol>
          </div>
          <div class="print-border">
            <app-heading title="消费明细"></app-heading>
            <ul style="margin-bottom: 10px">
              <li
                v-for="(item,index) in orderDetails.orderInfo"
                class="orderDetails-li"
              >
                <p class="serialNumber">{{index + 1}}、</p>
                <div class="details-list">
                  <div class="order-date-li">
                    <span class="order-label">
                      <span>{{item.name}}</span>
                      <span v-if="item.sku_name">| {{item.sku_name}}</span>
                    </span>
                    <span>￥{{item.price}}</span>
                  </div>
                  <div class="order-date-li">
                    <span class="order-label">数量</span>
                    <span>×{{item.num}}</span>
                  </div>
                  <div class="order-date-li" v-if="item.equity_type!=1">
                    <span class="order-label" v-if="item.equity_type==3">
                      次卡抵扣
                    </span>
                    <span class="order-label" v-if="item.equity_type==2">
                      充值卡折扣
                    </span>
                    <span v-if="item.equity_type!=4">
                      -￥{{item.reduceprice | formatMark}}
                    </span>
                  </div>
                  <div class="order-date-li" v-if="item.equity_type==4">
                    <span class="order-label" v-if="item.equity_type==4">
                      优惠金额
                    </span>
                    <span v-if="item.reduceprice.indexOf('-')==-1">
                      +￥{{item.reduceprice | formatMark}}
                    </span>
                    <span v-if="item.reduceprice.indexOf('-')!=-1">
                      -￥{{item.reduceprice | formatMark}}
                    </span>
                  </div>
                  <div class="order-date-li">
                    <span class="order-label">小计</span>
                    <span>￥{{item.Subtotal}}</span>
                  </div>
                </div>
              </li>
            </ul>
            <app-heading
              v-if="orderDetails.presentData && orderDetails.presentData.length>0"
              title="赠送明细"
            ></app-heading>
            <ul style="margin-bottom: 12px">
              <li
                v-for="(item,index) in orderDetails.presentData"
                class="orderDetails-li"
              >
                <p class="serialNumber">{{index + 1}}、</p>
                <div class="details-list">
                  <div class="order-date-li">
                    <span class="order-label">
                      <span v-if="item.itemType==1">{{item.name}}（服务）</span>
                      <span v-if="item.itemType==2 && !item.sku_name">
                        {{item.name}}（产品）
                      </span>
                      <span v-if="item.itemType==2 && item.sku_name">
                        {{item.name}}
                      </span>
                      <span v-if="item.sku_name">
                        | {{item.sku_name}}（产品）
                      </span>
                    </span>
                    <span>￥{{item.price | filterMoney}}</span>
                  </div>
                  <div class="order-date-li">
                    <span class="order-label">数量</span>
                    <span>×{{item.num}}</span>
                  </div>
                  <div class="order-date-li">
                    <span class="order-label">赠送状态</span>
                    <span v-if="item.status==0">仅选择</span>
                    <span v-if="item.status==1">已赠送</span>
                    <span v-if="item.status==2">已退回</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <ul
            class="print-border"
            style="border-bottom: 1px solid #e5e5e5"
            v-if="addressInfoFlag && orderDetails.address_info && orderDetails.address_info.id!=0"
          >
            <li class="order-date-li">
              <span class="order-label">收货人</span>
              <span>{{orderDetails.address_info.name}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">收货电话</span>
              <span>{{orderDetails.address_info.tel}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">收货地址</span>
              <span>{{orderDetails.address_info.address}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">配送费</span>
              <span v-if="orderDetails.dispatch_fee!='0.00'">
                ￥{{orderDetails.dispatch_fee}}
              </span>
              <span v-if="orderDetails.dispatch_fee=='0.00'">无</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.logistics">
              <span class="order-label">物流公司</span>
              <span>{{orderDetails.logistics}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.courier_num">
              <span class="order-label">物流单号</span>
              <span>{{orderDetails.courier_num}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">备注</span>
              <span v-if="orderDetails.remarks!=''">
                {{orderDetails.remarks}}
              </span>
              <span v-if="orderDetails.remarks==''">无</span>
            </li>
          </ul>
          <ul class="print-border" style="margin-top: 15px">
            <li class="order-date-li">
              <span class="order-label">合计</span>
              <span>￥{{orderDetails.summation | filterMoney}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.dismoney!='0.00'">
              <span class="order-label">充值卡折扣</span>
              <span>-￥{{orderDetails.dismoney}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.deduction!='0.00'">
              <span class="order-label">次卡抵扣</span>
              <span>-￥{{orderDetails.deduction}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.member_counpon_money">
              <span class="order-label">优惠券</span>
              <span>
                -￥{{orderDetails.member_counpon_money | filterMoney}}
              </span>
            </li>
            <li class="order-date-li" v-if="orderDetails.manuallys!='0.00'">
              <span class="order-label">优惠金额</span>
              <span v-if="orderDetails.manually>0">
                -￥{{orderDetails.manuallys}}
              </span>
              <span v-if="orderDetails.manually<0">
                +￥{{orderDetails.manuallys}}
              </span>
              <span v-if="orderDetails.manually==0">
                ￥{{orderDetails.manuallys}}
              </span>
            </li>
            <li class="order-date-li" v-if="orderDetails.small_change_money">
              <span class="order-label">抹零</span>
              <span>-￥{{orderDetails.small_change_money | filterMoney}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.dispatch_fee!='0.00'">
              <span class="order-label">运费</span>
              <span>+￥{{orderDetails.dispatch_fee}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">合计收款</span>
              <span>￥{{orderDetails.receivable}}</span>
            </li>
          </ul>
          <div class="print-scanCode">
            <img class="rwm" src="./images/fukuanma.png" alt="" />
            <p>扫码收藏店铺，享更多优惠哦！</p>
          </div>
        </div> -->
      </el-dialog>

      <!--默认打印-->
      <app-print
        style="display: none"
        :store="loginInfo"
        :order-info="orderDetails"
        :goods="orderDetails.orderInfo"
        :member="orderDetails.vip"
        :gift="orderDetails.presentData"
        :print-type="2"
      ></app-print>
      <!--打印样式-->
      <div
        class="printWrap"
        :style="{width:paperwidth +'px'}"
        ref="printorderstr"
        style="display: none; top: 150px"
      >
        <template v-for="(item,index) in printSet.set">
          <template v-if="item.name=='store'">
            <app-store :store-set="item.set" :store="loginInfo"></app-store>
          </template>
          <template v-if="item.name=='header'">
            <app-header
              :header-set="item.set"
              :order-header="orderDetails"
            ></app-header>
          </template>
          <template v-if="item.name=='goods'">
            <app-goods
              :goods-set="item.set"
              :goods="orderDetails.orderInfo"
            ></app-goods>
          </template>
          <template v-if="item.name=='goods'">
            <app-gift
              :goods-set="item.set"
              :gift="orderDetails.presentData"
            ></app-gift>
          </template>
          <template v-if="item.name=='vip'">
            <app-vip
              :vip-set="item.set"
              :member="orderDetails.vip"
              :order-details="orderDetails"
              :print-type="2"
            ></app-vip>
          </template>
          <!-- <template v-if="item.name=='takegoods'">
            <app-address
              :address-set="item.set"
              :order-info="orderDetails"
            ></app-address>
          </template> -->
          <template v-if="item.name=='footer'">
            <app-footer
              :footer-set="item.set"
              :order-footer="orderDetails"
              :print-type="2"
            ></app-footer>
          </template>
          <template v-if="item.name=='line'">
            <app-line :line-set="item.set"></app-line>
          </template>
          <template v-if="item.name=='info'">
            <app-info :info-set="item.set"></app-info>
          </template>
          <template v-if="item.name=='text'">
            <app-text :text-set="item.set"></app-text>
          </template>
        </template>
      </div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/unocss.theme.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="js/ff_util.js"></script>
  <script src="component/components.js"></script>
  <script src="print/print.js"></script>
  <script src="js/order.js"></script>
  <script type="text/javascript" src="js/plugin/LodopFuncs.js"></script>
  <script src="js/plugin/Qrcode.js"></script>
  <script src="js/plugin/JsBarcode.js"></script>
</html>
